import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import Exporting from "highcharts/modules/exporting";
import ExportData from "highcharts/modules/export-data";
import {
  Box,
  CircularProgress,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  styled
} from "@mui/material";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

if (typeof Highcharts === "object") {
  if (!Highcharts.Chart.prototype.exportChart) Exporting(Highcharts);
  if (!Highcharts.Chart.prototype.downloadCSV) ExportData(Highcharts);
}

// Styled container for the graph
const GraphContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  marginBottom: '16px',
  flexDirection: 'column',
  '& .highcharts-container': {
    width: '100% !important',
    height: '100% !important',
  },
}));

// Styled loading container
const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  height: '100%',
  padding: theme.spacing(4),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  border: `1px dashed ${theme.palette.divider}`,
}));

// Styled accordion summary with title background
const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  backgroundColor: '#281e50',
  borderBottom: '1px solid rgba(0, 0, 0, .125)',
  marginBottom: -1,
  '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
    transform: 'rotate(180deg)',
  },
  '& .MuiAccordionSummary-content': {
    marginLeft: theme.spacing(1),
  },
}));

const VisualizationGraph = ({ title, subtitle, categories, series, loading }) => {
  
  const exportButtons = [
    "viewFullscreen",
    "printChart",
    "downloadCSV"
  ]

  const exportIcon = "url(https://www.svgrepo.com/show/527693/download-minimalistic.svg)"

  const exportIconAxisX = 26
  const exportIconAxisY = 28
  const exportIconSize = 22

  // Enhanced chart options
  const options = {
    chart: {
      type: "column",
      backgroundColor: 'transparent',
      style: {
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      },
    },
    title: {
      text: null,
    },
    xAxis: {
      categories: categories,
      labels: {
        style: {
          fontSize: '12px',
        },
      },
      title: {
        text: null,
      },
    },
    yAxis: {
      title: {
        text: subtitle,
        style: {
          fontSize: '14px',
          color: '#555',
        },
      },
      labels: {
        formatter: function () {
          return this.value.toLocaleString();
        },
        style: {
          fontSize: '12px',
        },
      },
    },
    legend: {
      layout: 'horizontal',
      align: 'center',
      verticalAlign: 'bottom',
      itemStyle: {
        fontSize: '12px',
      },
      backgroundColor: 'rgba(255, 255, 255, 0.7)',
      borderWidth: 0,
      borderRadius: 5,
      padding: 10,
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#90caf9',
      borderRadius: 5,
      borderWidth: 1,
      shadow: true,
      style: {
        fontSize: '12px',
      },
      formatter: function () {
        return `<b>${this.series.name}</b><br/>${categories[this.x]}: ${this.y.toLocaleString()}`;
      },
    },
    plotOptions: {
      column: {
        dataLabels: {
          enabled: true,
          formatter: function () {
            return this.y.toLocaleString();
          },
          style: {
            fontSize: '10px',
            fontWeight: 'bold',
            color: '#333',
          },
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderRadius: 3,
          padding: 2,
        },
        borderWidth: 0,
        borderRadius: 4,
        pointPadding: 0.2,
      },
    },
    series: series.map(s => ({
      ...s,
      color: series.indexOf(s) === 0 ? '#4caf50' :
        series.indexOf(s) === 1 ? '#2196f3' : '#ff9800',
    })),
    credits: {
      enabled: false,
    },
     exporting: {
                enabled: true,
                buttons: {
                  contextButton: {
                    menuItems: exportButtons || [],
                    symbol:exportIcon,
                    symbolSize: exportIconSize,
                    symbolX: exportIconAxisX,
                    symbolY: exportIconAxisY,
                  },
                },
              },
  };

  // Handle loading state
  const renderChartContent = () => {
    if (loading) {
      return (
        <LoadingContainer>
          <CircularProgress size={60} thickness={4} />
          <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
            Loading chart data...
          </Typography>
        </LoadingContainer>
      );
    }

    // Handle empty data state
    if (!categories || categories.length === 0 || !series || series.length === 0) {
      return (
        <LoadingContainer>
          <Typography variant="h6" color="text.secondary">
            No data available for the selected filters
          </Typography>
        </LoadingContainer>
      );
    }

    return (
      <GraphContainer>
        <HighchartsReact
          highcharts={Highcharts}
          options={options}
          containerProps={{ style: { height: '100%', width: '100%' } }}
        />
      </GraphContainer>
    );
  };

  return (
    <Accordion defaultExpanded>
      <StyledAccordionSummary
        expandIcon={<ExpandMoreIcon sx={{color:'white'}}/>}
        aria-controls="graph-content"
        id="graph-header"
        color="white"
      >
        <Typography
          variant="h6"
          sx={{
            letterSpacing: '1px',
            color: 'white'
          }}
        >
          {title}
        </Typography>
      </StyledAccordionSummary>
      <AccordionDetails sx={{ p: 0, height: '400px' }}>
        {renderChartContent()}
      </AccordionDetails>
    </Accordion>
  );
};

export default VisualizationGraph;