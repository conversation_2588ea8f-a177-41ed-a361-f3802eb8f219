import React, { useEffect, useRef, useState } from "react";
import {
    FilterList,
} from "@mui/icons-material";
import {
    Button,
    Grid,
    styled,
    Snackbar,
} from "@mui/material";
import MuiAlert from "@mui/material/Alert";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import Footer from "../../../Components/Footer";
import VisualizationGraph from "../components/VisualizationGraph";
import useFilterState from "../hooks/useFilterState";
import useReportData from "../hooks/useReportData";
import ReportFilter from "../components/ReportFilter";
import ActiveFilters from "../components/ActiveFilters";
import ReportTable from "../components/ReportTable";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const initialFilters = {
    sites: [],
    states: [],
    financialYear: null,
    startDateTime: "",
    endDateTime: "",
};

const buildQueryString = (filterValues) => {
    const queryParts = [];

    // Add sites parameters
    filterValues.sites?.forEach((site, index) => {
        queryParts.push(`stores[${index}]=${site.id}`);
    });

    // Add states parameters
    filterValues.states?.forEach((state, index) => {
        queryParts.push(`states[${index}]=${state.state}`);
    });

    // Add financial year
    if (filterValues.financialYear) {
        queryParts.push(`financial_year=${filterValues.financialYear.id}`);
    }

    // Add date range
    if (filterValues.startDateTime && filterValues.endDateTime) {
        queryParts.push(`start_date=${filterValues.startDateTime}`);
        queryParts.push(`end_date=${filterValues.endDateTime}`);
    }
    // Return with leading '&' if parameters exist
    return queryParts.length ? `&${queryParts.join('&')}` : '';
};

const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
    },
}));


const TotalReedemedBenefits = (props) => {
    const { getTokenRefreshed: refresh, open: tokenOpen, overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();
    const [filterOpen, setFilterOpen] = useState(false);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");

    const {
        filters,
        appliedFilters,
        updateFilter,
        resetFilters,
        applyFilters,
        hasActiveFilters
    } = useFilterState(initialFilters);


    const { rows, title, loading, error, refetch } = useReportData(props?.request_name, appliedFilters, buildQueryString);

    useEffect(() => {
        if (error) {
            setMessageState("error");
            setMessage(error.response.data.message);
            setOpen(true);
        }
    }, [error]);

    const [columns, setColumns] = useState([
        { id: "total_birthday_redemptions", name: "Total Birthday Redemptions" },
        { id: "total_cashback_redemptions", name: "Total Cashback Redemptions" },
    ]);

    useEffect(() => {
        let newColumns = [
            { id: "total_birthday_redemptions", name: "Total Birthday Redemptions" },
            { id: "total_cashback_redemptions", name: "Total Cashback Redemptions" },
        ];

        if (appliedFilters.sites.length > 0) {
            newColumns.unshift({ id: "store_name", name: "Site" });
        } else {
            newColumns.unshift({ id: "state_name", name: "State" });
        }

        setColumns(newColumns);
    }, [appliedFilters.sites, appliedFilters.states]);

    const handleFilterSubmit = () => {
        applyFilters();
    };

    const handleFilterReset = () => {
        resetFilters();
    };

    const isRemoving = useRef(false);
    const removeFilter = (filterName) => {
        isRemoving.current = true;

        if (filterName === "dateRange") {
            // Clear both date properties
            updateFilter("startDateTime", "");
            updateFilter("endDateTime", "");
        } else {
            // Handle other filter types
            updateFilter(
                filterName,
                filterName === "sites"
                    ? []
                    : filterName === "states"
                        ? []
                        : filterName === "financialYear"
                            ? null
                            : undefined
            );
        }
    };

    useEffect(() => {
        if (isRemoving.current) {
            applyFilters();
            isRemoving.current = false;
        }
    }, [filters, applyFilters]);

    const handleClose = (event, reason) => {
        if (reason === "clickaway") return;
        setOpen(false);
        setTokenOpen(false);
    };

    const getGraphTitle = () => {
        if (filters.sites.length > 0) {
            return `Site-wise Total Reedemed Benefit ${title}`;
        } else {
            return `State-wise Total Reedemed Benefit ${title}`;
        }
    };

    function transformSalesData(rawData) {

        if (!rawData || rawData.length === 0) return { categories: [], series: [] };

        // Determine category key (state_name or store_name)
        const categoryKey = rawData[0].state_name
            ? "state_name"
            : rawData[0].store_name
                ? "store_name"
                : null;

        if (!categoryKey) {
            throw new Error("No valid category key found in API response");
        }

        // Extract categories
        const categories = rawData.map((item) => item[categoryKey]);

        // Define keys to exclude from series
        const excludeKeys = ["passport_holders_percentage", "other_sales_percentage"];

        // Get all numeric fields except categoryKey and excluded keys
        const numericKeys = Object.keys(rawData[0]).filter(
            (key) => key !== categoryKey && !excludeKeys.includes(key)
        );

        // Build Highcharts series
        const series = numericKeys.map((key) => ({
            name: key
                .replace(/_/g, " ") // Replace underscores with spaces
                .replace(/\b\w/g, (c) => c.toUpperCase()), // Capitalize words
            data: rawData.map((item) => parseFloat(item[key])),
        }));
        // console.log(rawData.slice(0, -1))

        return { categories, series };
    }

    const graphData = rows.slice(0, -1);

    const { categories, series } = transformSalesData(graphData);

    return (
        <div>
            <Grid container spacing={2}>
                <Grid item md={8} xs={12}>
                    <Header>
                        <h1>Total Redeemed Benefits Report</h1>
                    </Header>
                </Grid>
                <Grid
                    item
                    md={4}
                    xs={12}
                    display="flex"
                    alignItems="center"
                    justifyContent="flex-end"
                >
                    <Button color="primary" variant="contained" onClick={() => setFilterOpen(!filterOpen)}>
                        Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
                    </Button>
                </Grid>

                <ReportFilter
                    open={filterOpen}
                    filters={filters}
                    onFilterChange={updateFilter}
                    onApply={handleFilterSubmit}
                    onReset={handleFilterReset}
                />

                {hasActiveFilters && (
                    <ActiveFilters
                        filters={appliedFilters}
                        onRemoveFilter={removeFilter}
                    />
                )}

                <Grid item xs={12}>
                    <VisualizationGraph
                        title={getGraphTitle()}
                        subtitle={"Total No. of Redemptions"}
                        categories={categories}
                        series={series}
                        loading={loading}
                    />
                </Grid>

                <Grid item xs={12} mb={4}>
                    <ReportTable
                        columns={columns}
                        rows={rows}
                        loading={loading}
                    />
                </Grid>

                <Footer overlay={overlay || props.overlayNew} />
            </Grid>

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open || tokenOpen}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState || tokenMessageState}
                    sx={{ width: "100%" }}
                >
                    {message || tokenMessage}
                </Alert>
            </Snackbar>
        </div>
    );
};
export default TotalReedemedBenefits;