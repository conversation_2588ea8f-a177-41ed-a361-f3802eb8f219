import { useMemo } from "react";

export function useFinancialYears(startYear=2024) {
    return useMemo(() => {
        const today = new Date();
        const currentYear = today.getFullYear();
        const currentMonth = today.getMonth() + 1;
        const endYear = currentMonth < 7 ? currentYear : currentYear + 1;

        const years = [];
        for (let y = endYear; y >= startYear + 1; y--) {
            years.push({ id: `${y - 1}-${y}`, name: `${y - 1}-${y}` });
        }

        return years;
    }, [startYear]);
}
