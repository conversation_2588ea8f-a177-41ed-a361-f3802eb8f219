import React, { useEffect, useRef, useState } from "react";
import {
    FilterList
} from "@mui/icons-material";
import {
    Button,
    Grid,
    styled,
    Snackbar,
    Autocomplete,
    Checkbox,
    TextField,
    Box,
    Card,
    InputLabel,
    Collapse
} from "@mui/material";
import MuiAlert from "@mui/material/Alert";
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Footer from "../../../Components/Footer";
import ReportTable from "../components/ReportTable";
import VisualizationGraph from "../components/VisualizationGraph";
import ActiveFilters from "../components/ActiveFilters";
import useReportData from "../hooks/useReportData";
import { useStateSiteData } from "../hooks/useStateSiteData";
import { useFinancialYears } from "../hooks/useFinancialYears";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import httpclient from "../../../Utils";
const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});
const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;
const initialFilters = {
    sites: [],
    states: [],
    financialYear: null,
    startDateTime: null,
    endDateTime: null,
};
// Helper functions for date handling
const formatDateToYYYYMMDD = (date) => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};
const convertToIncludeDates = (dateData) => {
    if (!Array.isArray(dateData)) {
        console.error("Invalid input: Expected an array");
        return [];
    }
    return dateData
        .map(item => {
            if (!item || !item.date) {
                console.warn("Invalid item: Missing date property", item);
                return null;
            }
            // Parse date string and create date-only object
            const [year, month, day] = item.date.split('-').map(Number);
            if (year && month && day) {
                return new Date(year, month - 1, day);
            }
            console.warn("Invalid date format:", item.date);
            return null;
        })
        .filter(dateObj => dateObj !== null);
};
const buildQueryString = (filterValues) => {
    let queryParts = [];
    // Add sites parameters
    if (filterValues.sites?.length > 0) {
        filterValues.sites.forEach((site, index) => {
            queryParts.push(`stores[${index}]=${site.id}`);
        });
    }
    // Add states parameters
    if (filterValues.states?.length > 0) {
        filterValues.states.forEach((state, index) => {
            queryParts.push(`states[${index}]=${state.state}`);
        });
    }
    // Add financial year
    if (filterValues.financialYear) {
        queryParts.push(`financial_year=${filterValues.financialYear.id}`);
    }
    // Add start date (independent of end date)
    if (filterValues.startDateTime) {
        queryParts.push(`start_date=${formatDateToYYYYMMDD(filterValues.startDateTime)}`);
    }
    // Add end date (independent of start date)
    if (filterValues.endDateTime) {
        queryParts.push(`end_date=${formatDateToYYYYMMDD(filterValues.endDateTime)}`);
    }
    // Return with leading '&' if parameters exist
    return queryParts.length > 0 ? `&${queryParts.join('&')}` : '';
};
// Styled Components
const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
        fontWeight: 600,
        fontSize: "1.75rem",
    },
}));
const FilterCard = styled(Card)(({ theme }) => ({
    marginBottom: theme.spacing(3),
    borderRadius: 12,
    boxShadow: "0 4px 12px rgba(0,0,0,0.08)",
    overflow: "visible",
}));
const CustomReportFilter = ({
    open,
    filters,
    states,
    sites,
    financialYearOptions,
    onFilterChange,
    onApply,
    onReset,
    includeDates
}) => {
    return (
        <Grid item xs={12}>
            <Collapse in={open}>
                <FilterCard>
                    <Box p={3}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={4}>
                                <InputLabel>States</InputLabel>
                                <Autocomplete
                                    options={states}
                                    onChange={(event, newValue) => onFilterChange("states", newValue)}
                                    multiple
                                    id="states-filter"
                                    disableCloseOnSelect
                                    value={filters.states}
                                    getOptionLabel={(option) => option.state}
                                    renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                            <Checkbox
                                                icon={icon}
                                                checkedIcon={checkedIcon}
                                                style={{ marginRight: 8 }}
                                                checked={selected}
                                            />
                                            {option.state}
                                        </li>
                                    )}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="outlined"
                                            placeholder="Select states"
                                        />
                                    )}
                                    disabled={filters.sites.length > 0}
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <InputLabel>Sites</InputLabel>
                                <Autocomplete
                                    options={sites}
                                    onChange={(event, newValue) => onFilterChange("sites", newValue)}
                                    multiple
                                    id="sites-filter"
                                    disableCloseOnSelect
                                    value={filters.sites}
                                    getOptionLabel={(option) => option.manualStoreCode}
                                    renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                            <Checkbox
                                                icon={icon}
                                                checkedIcon={checkedIcon}
                                                style={{ marginRight: 8 }}
                                                checked={selected}
                                            />
                                            {option.manualStoreCode}
                                        </li>
                                    )}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="outlined"
                                            placeholder="Select sites"
                                        />
                                    )}
                                    disabled={filters.states.length > 0}
                                />
                            </Grid>
                            <Grid item>
                                <InputLabel>Zont Mile Sales Day</InputLabel>
                                <DatePicker
                                    selected={filters.startDateTime}
                                    onChange={(date) => {
                                        if (date) {
                                            const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                                            onFilterChange("startDateTime", dateOnly);
                                        } else {
                                            onFilterChange("startDateTime", null);
                                        }
                                    }}
                                    dateFormat="MMMM d, yyyy"
                                    placeholderText="Select Zont Miles Day"
                                    includeDates={includeDates}
                                    maxDate={filters.endDateTime}
                                    customInput={
                                        <TextField
                                            variant="outlined"
                                            fullWidth
                                            InputProps={{
                                                readOnly: true,
                                            }}
                                        />
                                    }
                                    disabled={filters.financialYear !== null}
                                />
                            </Grid>
                            <Grid item>
                                <InputLabel>Comparison Date</InputLabel>
                                <DatePicker
                                    selected={filters.endDateTime}
                                    onChange={(date) => {
                                        if (date) {
                                            const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                                            onFilterChange("endDateTime", dateOnly);
                                        } else {
                                            onFilterChange("endDateTime", null);
                                        }
                                    }}
                                    dateFormat="MMMM d, yyyy"
                                    placeholderText="Select end date"
                                    filterDate={(date) => {
                                        if (!filters.startDateTime) return true;
                                        const start = new Date(
                                            filters.startDateTime.getFullYear(),
                                            filters.startDateTime.getMonth(),
                                            filters.startDateTime.getDate()
                                        );
                                        const test = new Date(
                                            date.getFullYear(),
                                            date.getMonth(),
                                            date.getDate()
                                        );
                                        return test.getTime() !== start.getTime();
                                    }}
                                    customInput={
                                        <TextField
                                            variant="outlined"
                                            fullWidth
                                            InputProps={{
                                                readOnly: true,
                                            }}
                                        />
                                    }
                                    disabled={filters.financialYear !== null}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <Box display="flex" justifyContent="space-between" mt={2}>
                                    <Button
                                        variant="outlined"
                                        color="primary"
                                        onClick={onReset}
                                    >
                                        Reset Filters
                                    </Button>
                                    <Button
                                        variant="contained"
                                        color="primary"
                                        onClick={onApply}
                                    >
                                        Apply Filters{" "}
                                        <ArrowForwardIcon
                                            fontSize="small"
                                            style={{ marginLeft: "5px" }}
                                        />
                                    </Button>
                                </Box>
                            </Grid>
                        </Grid>
                    </Box>
                </FilterCard>
            </Collapse>
        </Grid>
    );
};
const DoubleZontMileSales = (props) => {
    const { getTokenRefreshed: refresh, open: tokenOpen, overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();
    const [filterOpen, setFilterOpen] = useState(false);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    // Filter state
    const [filters, setFilters] = useState(initialFilters);
    const [appliedFilters, setAppliedFilters] = useState(initialFilters);
    const [hasApplied, setHasApplied] = useState(false);
    // Date restrictions
    const [includeDates, setIncludeDates] = useState([]);
    useEffect(() => {
        const fetchDateData = async () => {
            try {
                const response = await httpclient.get('/request-response?requestName=lightspeed/advanced-report/double-zont-mile-days');
                if (response.status === 200) {
                    const dates = convertToIncludeDates(response.data);
                    setIncludeDates(dates);
                }
                else {
                    setOpen(true);
                    setMessage("Failed to fetch Customer Details");
                    setMessageState("error");
                }
            } catch (error) {
                console.log("Error fetching date data:", error);
            }
        };
        fetchDateData();
    }, []);
    const financialYearOptions = useFinancialYears()
    const { states, sites, loading: statesLoading, error: statesError } = useStateSiteData()
    const { rows, title, loading, error, refetch } = useReportData(props?.request_name, appliedFilters, buildQueryString);

    useEffect(() => {
        if (error) {
            setMessageState("error");
            setMessage(error.response.data.message);
            setOpen(true);
        }
    }, [error]);

    const [columns, setColumns] = useState([
        { id: "double_zont_mile_day_sales", name: "Double Zont Mile Day Sales", formatCurrency: true },
        { id: "other_day_sales", name: "Other Day Sales", formatCurrency: true },
    ]);
    useEffect(() => {
        let newColumns = [
            { id: "double_zont_mile_day_sales", name: "Double Zont Mile Day Sales", formatCurrency: true },
            { id: "other_day_sales", name: "Other Day Sales", formatCurrency: true },
        ];
        if (appliedFilters.sites.length > 0) {
            newColumns.unshift({ id: "store_name", name: "Site" });
        } else {
            newColumns.unshift({ id: "state_name", name: "State" });
        }
        setColumns(newColumns);
    }, [appliedFilters.sites, appliedFilters.states]);
    // Filter functions
    const updateFilter = (name, value) => {
        setFilters(prev => {
            const newFilters = { ...prev, [name]: value };
            // Handle mutual exclusivity
            if (name === "sites" && value.length > 0) {
                newFilters.states = [];
            } else if (name === "states" && value.length > 0) {
                newFilters.sites = [];
            }
            if (name === "financialYear" && value) {
                newFilters.startDateTime = null;
                newFilters.endDateTime = null;
            } else if ((name === "startDateTime" || name === "endDateTime") && value) {
                newFilters.financialYear = null;
            }
            return newFilters;
        });
    };
    const resetFilters = () => {
        setFilters(initialFilters);
        setAppliedFilters(initialFilters);
        setHasApplied(false);
    };
    const applyFilters = () => {
        setAppliedFilters(filters);
        setHasApplied(true);
    };
    const hasActiveFilters = hasApplied && Object.values(appliedFilters).some(value =>
        Array.isArray(value) ? value.length > 0 : value !== null && value !== ""
    );
    const isRemoving = useRef(false);
    const removeFilter = (filterName) => {
        isRemoving.current = true;
        if (filterName === "dateRange") {
            updateFilter("startDateTime", null);
            updateFilter("endDateTime", null);
        } else {
            updateFilter(
                filterName,
                filterName === "sites"
                    ? []
                    : filterName === "states"
                        ? []
                        : filterName === "financialYear"
                            ? null
                            : undefined
            );
        }
    };
    useEffect(() => {
        if (isRemoving.current) {
            applyFilters();
            isRemoving.current = false;
        }
    }, [filters, applyFilters]);
    const handleClose = (event, reason) => {
        if (reason === "clickaway") return;
        setOpen(false);
        setTokenOpen(false);
    };
    const getGraphTitle = () => {
        if (filters.sites.length > 0) {
            return `Site-wise Double Zont Miles Sales ${title}`;
        } else {
            return `State-wise Double Zont Miles Sales ${title}`;
        }
    };
    function transformSalesData(rawData) {
        if (!rawData || rawData.length === 0) return { categories: [], series: [] };
        // Pick category key automatically (state_name or store_name)
        const categoryKey = rawData[0].state_name
            ? "state_name"
            : rawData[0].store_name
                ? "store_name"
                : null;
        if (!categoryKey) {
            throw new Error("No valid category key found in API response");
        }
        // Categories for x-axis
        const categories = rawData.map((item) => item[categoryKey]);
        // Define keys to exclude from series
        const excludeKeys = ["avg_time_between_orders_hours"];
        // Numeric fields (all keys except the category key and excluded keys)
        const numericKeys = Object.keys(rawData[0])
            .filter((key) => key !== categoryKey && !excludeKeys.includes(key));
        // Build Highcharts series
        const series = numericKeys.map((key) => ({
            name: key
                .replace(/_/g, " ")
                .replace(/\b\w/g, (c) => c.toUpperCase()),
            data: rawData.map((item) => parseFloat(item[key])),
        }));
        return { categories, series };
    }
    const graphData = rows.slice(0, -1);
    const { categories, series } = transformSalesData(graphData);
    return (
        <div>
            <Grid container spacing={2}>
                <Grid item md={8} xs={12}>
                    <Header>
                        <h1>Double Zont Miles Sales Report</h1>
                    </Header>
                </Grid>
                <Grid
                    item
                    md={4}
                    xs={12}
                    display="flex"
                    alignItems="center"
                    justifyContent="flex-end"
                >
                    <Button
                        color="primary"
                        variant="contained"
                        onClick={() => setFilterOpen(!filterOpen)}
                        sx={{
                            borderRadius: 2,
                            px: 3,
                            py: 1.2,
                            fontWeight: 500,
                            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                            '&:hover': {
                                boxShadow: '0 6px 16px rgba(0,0,0,0.2)',
                            }
                        }}
                    >
                        Filter <FilterList style={{ marginLeft: "8px" }} fontSize="small" />
                    </Button>
                </Grid>
                {/* Custom Filter UI */}
                <CustomReportFilter
                    open={filterOpen}
                    filters={filters}
                    states={states}
                    sites={sites}
                    financialYearOptions={financialYearOptions}
                    onFilterChange={updateFilter}
                    onApply={applyFilters}
                    onReset={resetFilters}
                    includeDates={includeDates}
                />
                {hasActiveFilters && (
                    <ActiveFilters
                        filters={appliedFilters}
                        onRemoveFilter={removeFilter}
                        showTime={false}
                    />
                )}
                <Grid item xs={12} mb={4}>
                    <VisualizationGraph
                        title={getGraphTitle()}
                        subtitle={"No. of Sales"}
                        categories={categories}
                        series={series}
                        loading={loading}
                    />
                </Grid>
                <Grid item mb={8} xs={12}>
                    <ReportTable
                        columns={columns}
                        rows={rows}
                        loading={loading}
                    />
                </Grid>
                <Footer overlay={overlay || props.overlayNew} />
            </Grid>
            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open || tokenOpen}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState || tokenMessageState}
                    sx={{ width: "100%" }}
                >
                    {message || tokenMessage}
                </Alert>
            </Snackbar>
        </div>
    );
};
export default DoubleZontMileSales;