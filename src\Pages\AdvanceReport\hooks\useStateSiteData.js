import { useEffect, useState } from "react";
import httpclient from "../../../Utils";

export const useStateSiteData = () => {
  const [states, setStates] = useState([]);
  const [sites, setSites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        const [stateRes, siteRes] = await Promise.all([
          httpclient.get("/request-response?requestName=lightspeed/advanced-report/states"),
          httpclient.get("/request-response?requestName=lightspeed/advanced-report/stores"),
        ]);

        setStates(stateRes?.data || []);
        setSites(siteRes?.data || []);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { states, sites, loading, error };
};
