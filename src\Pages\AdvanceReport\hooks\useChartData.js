import { useMemo } from 'react';

export const useChartsData = (
  apiData,
  options = {}
) => {
  const {
    categoryKey = 'name',
    valueKey = 'value',
    seriesName = 'Data',
    pieNameKey,
    pieValueKey,
    metricKeys,
    transformFunction,
    sortByValue = false,
    sortDescending = true
  } = options;

  return useMemo(() => {
    if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
      return {
        series: [{ name: seriesName, data: [] }],
        categories: []
      };
    }

    // Handle custom transformation if provided
    if (transformFunction) {
      return {
        series: [{ name: seriesName, data: transformFunction(apiData) }],
        categories: []
      };
    }

    // Handle single metric data (like first example)
    if (metricKeys && apiData.length === 1) {
      const data = metricKeys.map(key => ({
        name: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        y: parseFloat(apiData[0][key])
      }));
      
      return {
        series: [{ name: seriesName, data }],
        categories: metricKeys.map(key => 
          key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        )
      };
    }

    // Handle pie chart data
    if (pieNameKey && pieValueKey) {
      const data = apiData.map(item => ({
        name: item[pieNameKey],
        y: parseFloat(item[pieValueKey])
      }));
      
      return {
        series: [{ 
          type: 'pie', 
          name: seriesName, 
          data: sortByValue 
            ? [...data].sort((a, b) => sortDescending ? b.y - a.y : a.y - b.y)
            : data 
        }],
        categories: []
      };
    }

    // Handle standard categorical data (bar/column charts)
    const categories = apiData.map(item => item[categoryKey]);
    const data = apiData.map(item => ({
      name: item[categoryKey],
      y: parseFloat(item[valueKey]),
      ...item // Include additional properties
    }));

    // Sort by value if requested
    const sortedData = sortByValue
      ? [...data].sort((a, b) => sortDescending ? b.y - a.y : a.y - b.y)
      : data;
    
    const sortedCategories = sortByValue
      ? sortedData.map(item => item.name)
      : categories;

    return {
      series: [{ name: seriesName, data: sortedData }],
      categories: sortedCategories
    };
  }, [apiData, categoryKey, valueKey, seriesName, pieNameKey, pieValueKey, 
      metricKeys, transformFunction, sortByValue, sortDescending]);
};