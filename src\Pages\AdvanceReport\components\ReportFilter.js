import {
  Autocomplete,
  Checkbox,
  TextField,
  Box,
  Grid,
  Button,
  Card,
  InputLabel,
  Collapse,
} from '@mui/material';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import { useStateSiteData } from "../hooks/useStateSiteData";
import { useFinancialYears } from '../hooks/useFinancialYears';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const ReportFilter = ({
  open,
  filters,
  onFilterChange,
  onApply,
  onReset
}) => {
  const { states, sites} = useStateSiteData()
  const financialYearOptions = useFinancialYears()
  return (
    <Grid item xs={12}>
      <Collapse in={open}>
        <Card>
          <Box p={4}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <InputLabel>States</InputLabel>
                <Autocomplete
                  options={states}
                  onChange={(event, newValue) => onFilterChange("states", newValue)}
                  multiple
                  id="states-filter"
                  disableCloseOnSelect
                  value={filters.states}
                  getOptionLabel={(option) => option.state}
                  renderOption={(props, option, { selected }) => (
                    <li {...props}>
                      <Checkbox
                        icon={icon}
                        checkedIcon={checkedIcon}
                        style={{ marginRight: 8 }}
                        checked={selected}
                      />
                      {option.state}
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Select states"
                    />
                  )}
                  disabled={filters.sites.length > 0}
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <InputLabel>Sites</InputLabel>
                <Autocomplete
                  options={sites}
                  onChange={(event, newValue) => onFilterChange("sites", newValue)}
                  multiple
                  id="sites-filter"
                  disableCloseOnSelect
                  value={filters.sites}
                  getOptionLabel={(option) => option.manualStoreCode}
                  renderOption={(props, option, { selected }) => (
                    <li {...props}>
                      <Checkbox
                        icon={icon}
                        checkedIcon={checkedIcon}
                        style={{ marginRight: 8 }}
                        checked={selected}
                      />
                      {option.manualStoreCode}
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Select sites"
                    />
                  )}
                  disabled={filters.states.length > 0}
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <InputLabel>Financial Year</InputLabel>
                <Autocomplete
                  options={financialYearOptions}
                  onChange={(event, newValue) => onFilterChange("financialYear", newValue)}
                  value={filters.financialYear}
                  getOptionLabel={(option) => option.name}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Select financial year"
                    />
                  )}
                  disabled={filters.startDateTime !== "" || filters.endDateTime !== ""}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <InputLabel>Start Date & Time</InputLabel>
                <TextField
                  variant="outlined"
                  name="startDateTime"
                  type="datetime-local"
                  value={filters.startDateTime}
                  onChange={(e) => onFilterChange("startDateTime", e.target.value)}
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                  disabled={filters.financialYear !== null}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <InputLabel>End Date & Time</InputLabel>
                <TextField
                  variant="outlined"
                  name="endDateTime"
                  type="datetime-local"
                  value={filters.endDateTime}
                  onChange={(e) => onFilterChange("endDateTime", e.target.value)}
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                  disabled={filters.financialYear !== null}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Box display="flex" justifyContent="space-between">
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={onReset}
                  >
                    Reset Filters
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={onApply}
                  >
                    Apply Filters{" "}
                    <ArrowForwardIcon
                      fontSize="small"
                      style={{ marginLeft: "5px" }}
                    />
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Card>
      </Collapse>
    </Grid>
  );
};

export default ReportFilter;