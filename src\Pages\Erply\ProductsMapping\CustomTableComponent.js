import React from "react";
import { styled } from "@mui/material/styles";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  IconButton,
  Skeleton,
  TableFooter,
  TablePagination,
  useTheme,
  tableCellClasses,
} from "@mui/material";
import {
  KeyboardArrowDown,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  KeyboardArrowUp,
  UnfoldMore,
} from "@mui/icons-material";
import moment from "moment";
import PropTypes from "prop-types";

// Styled components
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.light,
    fontWeight: 600,
    fontSize: "0.875rem",
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
  "& svg": {
    position: "relative",
    top: "5px",
    cursor: "pointer",
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:last-child td, &:last-child th": {
    border: 0,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.selected,
    cursor: "pointer",
  },
}));

const LoadingContainer = styled(Box)({
  position: "relative",
  height: "465px",
  overflow: "hidden",
});

const NoDataContainer = styled(TableRow)({
  position: "relative",
  height: "50px",
});

const NoDataCell = styled(TableCell)({
  position: "absolute",
  right: "50%",
  borderBottom: "none",
  textAlign: "center",
  color: "#666",
  fontStyle: "italic",
});

// Table pagination actions component
function TablePaginationActions({ count, page, rowsPerPage, onPageChange }) {
  const theme = useTheme();

  const handleBackButtonClick = (event) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event) => {
    onPageChange(event, page + 1);
  };

  return (
    <div style={{ flexShrink: "0" }}>
      <IconButton
        onClick={handleBackButtonClick}
        disabled={page === 1}
        aria-label="previous page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowRight />
        ) : (
          <KeyboardArrowLeft />
        )}
      </IconButton>
      <IconButton
        onClick={handleNextButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage)}
        aria-label="next page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowLeft />
        ) : (
          <KeyboardArrowRight />
        )}
      </IconButton>
    </div>
  );
}

TablePaginationActions.propTypes = {
  count: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  page: PropTypes.number.isRequired,
  rowsPerPage: PropTypes.number.isRequired,
};

// Cell content renderer
const renderCellContent = (row, column) => {
  const value = row[column.id];

  // Handle different data types
  switch (column.id) {
    case "priceWithVat":
    case "total":
      return value ? `$${parseFloat(value).toFixed(2)}` : "-";
    
    case "lastModified":
    case "added":
    case "created_at":
    case "lastUpdated":
      return value ? moment(value).format("ddd, DD MMM YYYY, h:mm:ss a") : "-";
    
    case "date":
      return value ? moment(value).format("ddd, DD MMM YYYY") : "-";
    
    case "status":
      return value || "-";
    
    default:
      return value || "-";
  }
};

// Main table component
const CustomTableComponent = ({
  columns = [],
  rows = [],
  loading = false,
  sortable = true,
  currentColumn = "",
  direction = false,
  page = 1,
  total = 0,
  fromTable = 0,
  toTable = 0,
  rowsPerPage = 20,
  onSort,
  onView,
  onChangePage,
  onChangeRowsPerPage,
}) => {
  // Render table header
  const renderTableHeader = () => (
    <TableHead>
      <TableRow>
        {columns.map((column, index) => (
          <StyledTableCell
            key={column.id}
            onClick={sortable && onSort ? () => onSort(column.id) : undefined}
            style={{ cursor: sortable && onSort ? "pointer" : "default" }}
          >
            {currentColumn === column.id ? (
              <span style={{ fontWeight: "700" }}>{column.name}</span>
            ) : (
              column.name
            )}
            {sortable && onSort && (
              <>
                {currentColumn === column.id ? (
                  direction ? (
                    <KeyboardArrowUp fontSize="small" />
                  ) : (
                    <KeyboardArrowDown fontSize="small" />
                  )
                ) : (
                  <UnfoldMore fontSize="small" />
                )}
              </>
            )}
          </StyledTableCell>
        ))}
      </TableRow>
    </TableHead>
  );

  // Render loading skeleton
  const renderLoadingSkeleton = () => (
    <TableBody>
      {Array.from({ length: 7 }).map((_, rowIndex) => (
        <TableRow key={rowIndex}>
          {columns.map((column) => (
            <StyledTableCell key={column.id}>
              <Skeleton variant="text" height={40} />
            </StyledTableCell>
          ))}
        </TableRow>
      ))}
    </TableBody>
  );

  // Render table rows
  const renderTableRows = () => (
    <TableBody>
      {rows.length > 0 ? (
        rows.map((row, rowIndex) => (
          <StyledTableRow
            key={row.productID || rowIndex}
            onClick={() => onView && onView(row)}
            sx={{
              backgroundColor: (row.shopifyProductID && row.shopifyProductID !== "") ? "#e8f5e8 !important" : undefined,
            }}
          >
            {columns.map((column, colIndex) => (
              <StyledTableCell
                key={column.id}
                component={colIndex === 0 ? "th" : undefined}
                scope={colIndex === 0 ? "row" : undefined}
              >
                {renderCellContent(row, column)}
              </StyledTableCell>
            ))}
          </StyledTableRow>
        ))
      ) : (
        <NoDataContainer>
          <NoDataCell colSpan={columns.length}>
            No products found
          </NoDataCell>
        </NoDataContainer>
      )}
    </TableBody>
  );

  // Render table footer with pagination
  const renderTableFooter = () => (
    <TableFooter>
      <TableRow>
        <TablePagination
          rowsPerPageOptions={[20, 50, 70, 100]}
          rowsPerPage={rowsPerPage}
          page={page}
          count={total}
          SelectProps={{ native: true }}
          labelDisplayedRows={() =>
            `${fromTable || 0} - ${toTable || 0} of ${total}`
          }
          onPageChange={onChangePage}
          onRowsPerPageChange={onChangeRowsPerPage}
          ActionsComponent={TablePaginationActions}
        />
      </TableRow>
    </TableFooter>
  );

  return (
    <Paper sx={{ width: "100%", overflow: "hidden", marginBottom: "40px" }}>
      <TableContainer sx={{ maxHeight: 678 }}>
        <Table stickyHeader aria-label="products table" sx={{ minWidth: 700 }}>
          {renderTableHeader()}
          {loading ? renderLoadingSkeleton() : renderTableRows()}
          {renderTableFooter()}
        </Table>
      </TableContainer>
    </Paper>
  );
};

CustomTableComponent.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
    })
  ).isRequired,
  rows: PropTypes.array.isRequired,
  loading: PropTypes.bool,
  sortable: PropTypes.bool,
  currentColumn: PropTypes.string,
  direction: PropTypes.bool,
  page: PropTypes.number,
  total: PropTypes.number,
  fromTable: PropTypes.number,
  toTable: PropTypes.number,
  rowsPerPage: PropTypes.number,
  onSort: PropTypes.func,
  onView: PropTypes.func,
  onChangePage: PropTypes.func,
  onChangeRowsPerPage: PropTypes.func,
};

export default CustomTableComponent;




