// components/ReportTable.jsx
import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Skeleton,
  Typography
} from '@mui/material';

const ReportTable = ({ columns, rows, loading }) => {
  // Helper function to format currency with just $ prefix
  const formatCurrency = (value) => {
    if (typeof value !== 'number') return value;
    // Simply prepend $ without any formatting
    return `$${value}`;
  };

  const renderSkeletonRows = () => {
    return Array.from({ length: 5 }).map((_, rowIndex) => (
      <TableRow key={`skeleton-${rowIndex}`}>
        {columns.map((column) => (
          <TableCell key={`skeleton-${column.id}-${rowIndex}`}>
            <Skeleton variant="text" height={30} />
          </TableCell>
        ))}
      </TableRow>
    ));
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight: 600 }}>
        <Table stickyHeader aria-label="sticky table">
          <TableHead>
            <TableRow sx={{ backgroundColor: 'primary.main' }}>
              {columns.map((column) => (
                <TableCell
                  sx={{ backgroundColor: 'primary.main', color: 'white' }}
                  key={column.id}
                  align={column.align || 'left'}
                  style={{ minWidth: column.minWidth }}
                >
                  {column.name}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              renderSkeletonRows()
            ) : rows.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length} align="center">
                  <Typography>No records found</Typography>
                </TableCell>
              </TableRow>
            ) : (
              <>
                {/* Data rows */}
                {rows.map((row, index) => {
                  // Check if this is a subtotal row
                  const isSubtotalRow =
                    (row.state_name && row.state_name === "SUB TOTAL") ||
                    (row.store_name && row.store_name === "SUB TOTAL");

                  return (
                    <TableRow
                      hover
                      role="checkbox"
                      tabIndex={-1}
                      key={index}
                      sx={isSubtotalRow ? {
                        '& .MuiTableCell-root': {
                          fontWeight: 'bold',
                          backgroundColor: 'rgba(0, 0, 0, 0.04)'
                        }
                      } : {}}
                    >
                      {columns.map((column) => {
                        let displayValue;
                        // Special handling for passport_holders_sales
                        if (column.id === "passport_holders_sales") {
                          displayValue = `$${row[column.id] || 0} (${row["passport_holders_percentage"] || 0}%)`;
                        }
                        // Special handling for other_sales
                        else if (column.id === "non_passport_holder_sales") {
                          displayValue = `$${row[column.id] || 0} (${row["other_sales_percentage"] || 0}%)`;
                        }
                        else if (column.id === "passport_holders") {
                          displayValue = `${row[column.id] || 0} (${row["passport_holders_percentage"] || 0}%)`
                        }
                        else if (column.id === "non_passport_holders") {
                          displayValue = `${row[column.id] || 0} (${row["non_passport_holders_percentage"] || 0}%)`
                        }
                        else if (column.id === "total_amount_of_redeemed_benefits") {
                          displayValue = `$${row[column.id] || 0} (${row["redeemed_percentage"] || 0}%)`
                        }
                        else {
                          // Default handling with currency formatting
                          displayValue = row[column.id];
                          if (column.formatCurrency && typeof displayValue === 'number') {
                            displayValue = formatCurrency(displayValue);
                          }
                        }

                        return (
                          <TableCell
                            key={column.id}
                            align={column.align || 'left'}
                            sx={isSubtotalRow ? { fontWeight: 'bold' } : {}}
                          >
                            {displayValue}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  );
                })}
              </>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default ReportTable;