// hooks/useFilterState.js
import { useState } from 'react';

const useFilterState = (initialFilters) => {
  const [filters, setFilters] = useState(initialFilters);
  const [appliedFilters, setAppliedFilters] = useState(initialFilters);
  const [hasApplied, setHasApplied] = useState(false);

  const updateFilter = (name, value) => {
    setFilters(prev => {
      const newFilters = { ...prev, [name]: value };
      
      // Handle mutual exclusivity
      if (name === "sites" && value.length > 0) {
        newFilters.states = [];
      } else if (name === "states" && value.length > 0) {
        newFilters.sites = [];
      }
      
      if (name === "financialYear" && value) {
        newFilters.startDateTime = "";
        newFilters.endDateTime = "";
      } else if ((name === "startDateTime" || name === "endDateTime") && value) {
        newFilters.financialYear = null;
      }
      
      return newFilters;
    });
  };

  const resetFilters = () => {
    setFilters(initialFilters);
    setAppliedFilters(initialFilters);
    setHasApplied(false);
  };

  const applyFilters = () => {
    setAppliedFilters(filters);
    setHasApplied(true);
  };

  const hasActiveFilters = hasApplied && Object.values(appliedFilters).some(value =>
    Array.isArray(value) ? value.length > 0 : value !== null && value !== ""
  );

  return { 
    filters, 
    appliedFilters, 
    updateFilter, 
    resetFilters, 
    applyFilters, 
    hasActiveFilters 
  };
};

export default useFilterState;