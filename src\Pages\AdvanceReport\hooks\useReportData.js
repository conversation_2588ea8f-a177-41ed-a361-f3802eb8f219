// hooks/useReportData.js
import { useState, useEffect } from 'react';
import httpclient from '../../../Utils';

const useReportData = (url, appliedFilters, buildQueryString) => {
  const [rows, setRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [title,setTitle]=useState("")

  const fetchData = async () => {
    setLoading(true);
    try {
      const queryString = buildQueryString(appliedFilters);
      const { data } = await httpclient.get(`${url}${queryString}`);
      setTitle(data?.msg)
      setRows(data?.data);
      setError(null);
    } catch (err) {
      setError(err);
      console.error("Error fetching report data:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [appliedFilters]); // Only refetch when appliedFilters change

  return { rows,title, loading, error, refetch: fetchData };
};

export default useReportData;