import React, { useEffect, useState } from 'react';
import {
    Box,
    Typography,
    Button,
    Grid,
    Divider,
    Paper,
    Snackbar,
    Collapse,
    Modal,
    IconButton,
} from '@mui/material';
import MuiAlert from "@mui/material/Alert";
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import httpclient from '../../../Utils';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});
export default function CustomerDetails() {
    const { id: customerId } = useParams();
    const location = useLocation();
    const navigate = useNavigate();
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState('');
    const [messageState, setMessageState] = useState('success');
    const [isLoading, setIsLoading] = useState(false);
    const loggedInUser = localStorage.getItem('user');
    const username = JSON.parse(loggedInUser).user_name;
    const [customerData, setCustomerData] = useState(null);
    const [diableAll, setDisableAll] = useState(false);

    const [receiptData, setReceiptData] = useState({
        receiptNumber: '',
        receiptAmount: '',
    });
    const { permissions } = location.state || {};

    const handleChange = (e) => {
        const { name, value } = e.target;
        setReceiptData((prev) => ({
            ...prev,
            [name]: value,
        }));
        if (receiptErrors[name]) {
            setReceiptErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };
    
    // Add separate loading states for each form
    const [receiptLoading, setReceiptLoading] = useState(false);
    const [topupLoading, setTopupLoading] = useState(false);
    // Add validation error states
    const [receiptErrors, setReceiptErrors] = useState({});
    const [topupErrors, setTopupErrors] = useState({});

    useEffect(() => {
        fetchCustomerData(customerId);
    }, [customerId]);
    const fetchCustomerData = async (customerId) => {
        setIsLoading(true);
        try {
            const response = await httpclient.get(`request-response?requestName=lightspeed/customer-point-details/${customerId}`);
            if (response.status === 200) {
                setIsLoading(false);
                setCustomerData(response.data.data);
                if (response.data.data.liteCardMemberInfo == null) {
                    setDisableAll(true);
                }
            } else {
                setIsLoading(false);
                setOpen(true);
                setMessage("Failed to fetch Customer Details");
                setMessageState("error");
            }
        } catch (error) {
            setIsLoading(false);
            setOpen(true);
            setMessage("Failed to fetch Customer Details");
            setMessageState("error");
            return null;
        }
    };
    const handleApplyPoint = async (e) => {
        e.preventDefault();
        // Validate receipt form
        const errors = {};
        if (!receiptData.receiptNumber.trim()) {
            errors.receiptNumber = "Receipt number is required";
        }
        if (!receiptData.receiptAmount.trim()) {
            errors.receiptAmount = "Receipt amount is required";
        }
        if (Object.keys(errors).length > 0) {
            setReceiptErrors(errors);
            return;
        }
        try {
            setReceiptLoading(true);
            setReceiptErrors({});
            const payload = {
                synccare_internal_id: customerData?.customer?.id,
                receipt_number: receiptData.receiptNumber,
                receipt_value: receiptData.receiptAmount,
            };
            const response = await httpclient.post(`request-response?requestName=lightspeed/apply-point`, payload);
            if (response.status === 200) {
                setReceiptLoading(false);
                setOpen(true);
                setMessageState("success");
                setMessage(response.data?.message || "Added successfully");
                setReceiptData({
                    receiptNumber: '',
                    receiptAmount: '',
                });
                fetchCustomerData(customerId);
            } else {
                setOpen(true);
                setMessageState("error");
                setMessage(response?.data?.message || "An error occurred");
            }
        } catch (error) {
            setReceiptLoading(false);
            setOpen(true);
            setMessageState("error");
            const errMsg = error?.response?.data?.message || error?.message || "An error occurred";
            setMessage(errMsg);
        }
        finally {
            setReceiptLoading(false);
        }
    };
    const fetchPointDetails = async () => {
        navigate(`/customer-point-details/${customerData?.customer?.id}`);
    };
    const fetchTopupPoints=async()=>{
        navigate(`/toup-point-details/${customerData?.customer?.id}`);
    }
    const customerDetails = [
        { label: 'Full Name', value: customerData?.customer?.full_name || '-' },
        { label: 'Mobile', value: customerData?.customer?.phone || '-' },
        { label: 'Email', value: customerData?.customer?.email || '-' },
        { label: 'LiteCard Member ID', value: customerData?.liteCardMemberInfo?.external_id || '-' },
        { label: 'Lightspeed ID', value: customerData?.lightSpeedInfo?.external_id || '-' },
        { label: 'Current Points', value: customerData?.customer?.current_points ? parseFloat(customerData?.customer?.current_points).toFixed(0) : '0' },
        { label: 'Manual TopUp Points', value: customerData?.manual_additon || '0' },
        { label: 'Manual Deducted TopUp Points', value: customerData?.manual_deduction || '0' },
        { label: 'Points Redeemed', value: customerData?.customer?.points_redeemed || '0' },
        { label: 'Last Point Updated', value: customerData?.customer?.last_point_update || '-' },
    ];
    // Add state variables for collapse and topup
    const [receiptFormOpen, setReceiptFormOpen] = useState(false);
    const [topupFormOpen, setTopupFormOpen] = useState(false);
    const [topupAmount, setTopupAmount] = useState('');
    // Add state for remarks
    // const [topupRemarks, setTopupRemarks] = useState('');
    
    // Add topup handler
    const handleTopupChange = (e) => {
        setTopupAmount(e.target.value);
        if (topupErrors.topupAmount) {
            setTopupErrors({});
        }
    };
    
    // Add handler for remarks
    // const handleRemarksChange = (e) => {
    //     setTopupRemarks(e.target.value);
    // };
    
    const handleTopupPoint = async (e) => {
        e.preventDefault();
        // Validate topup form
        const errors = {};
        if (!topupAmount.trim()) {
            errors.topupAmount = "Amount is required";
        }
        if (Object.keys(errors).length > 0) {
            setTopupErrors(errors);
            return;
        }
        try {
            setTopupLoading(true);
            setTopupErrors({});
            const payload = {
                customer_id: customerData?.customer?.id,
                points: topupAmount,
                username: username,
                // remarks: topupRemarks // Add remarks to payload
            };
            const response = await httpclient.post(`request-response?requestName=lightspeed/topup-points`, payload);
            if (response.status === 200) {
                setTopupLoading(false);
                setOpen(true);
                setMessageState("success");
                setMessage(response.data?.message || "TopUp successful");
                setTopupAmount('');
                // setTopupRemarks(''); // Reset remarks after submission
                fetchCustomerData(customerId);
            } else {
                setOpen(true);
                setMessageState("error");
                setMessage(response?.data?.message || "An error occurred");
            }
        } catch (error) {
            setTopupLoading(false);
            setOpen(true);
            setMessageState("error");
            const errMsg = error?.response?.data?.message || error?.message || "An error occurred";
            setMessage(errMsg);
        }
        finally {
            setTopupLoading(false);
        }
    };
    // Add state for the points adjustment modal
    const [pointsModalOpen, setPointsModalOpen] = useState(false);
    const [pointsAdjustment, setPointsAdjustment] = useState(0);
    const [adjustmentLoading, setAdjustmentLoading] = useState(false);
    // Handler for adjusting points
    const handleAdjustPoints = async () => {
        try {
            setAdjustmentLoading(true);
            const payload = {
                customer_id: customerData?.customer?.id,
                points: pointsAdjustment,
                username: username
            };
            const response = await httpclient.post(`request-response?requestName=lightspeed/deduct-points`, payload);
            if (response.status === 200) {
                setOpen(true);
                setMessageState("success");
                setMessage(response.data?.message || "Points adjusted successfully");
                setPointsModalOpen(false);
                setPointsAdjustment(0);
                fetchCustomerData(customerId);
            } else {
                setOpen(true);
                setMessageState("error");
                setMessage(response?.data?.message || "An error occurred");
            }
        } catch (error) {
            setOpen(true);
            setMessageState("error");
            const errMsg = error?.response?.data?.message || error?.message || "An error occurred";
            setMessage(errMsg);
        } finally {
            setAdjustmentLoading(false);
        }
    };
    return (
        <Box
            sx={{
                minHeight: '100vh',
                p: 2,
            }}
        >
            <Paper
                elevation={3}
                sx={{
                    width: '100%',
                    p: 4,
                    borderRadius: 2,
                }}
            >
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                    Customer Details <span style={{ color: '#FFBF00', fontSize: '18px' }}>{diableAll ? ' (LiteCard Member ID not found)' : ''}</span>
                </Typography>
                <Grid container spacing={2} mt={2}>
                    {customerDetails.map((item, index) => (
                        <Grid item xs={6} key={index}>
                            <Box display="flex" alignItems="center">
                                <Typography fontWeight="bold">
                                    {item.label}:
                                </Typography>
                                <Typography fontWeight="500" sx={{ ml: 1 }}>{item.value || '-'}</Typography>
                                {item.label === 'Current Points' && diableAll === false && permissions?.some((pre) => pre.name === "allow_create" && pre.status === 1) && (
                                    <Button
                                        size="small"
                                        color="primary"
                                        variant='outlined'
                                        sx={{ ml: 1, minWidth: 'auto', p: '2px 8px' }}
                                        onClick={() => {
                                            setPointsModalOpen(true);
                                            setPointsAdjustment(0);
                                        }}
                                    >
                                        Deduct Points
                                    </Button>
                                )}
                            </Box>
                        </Grid>
                    ))}
                </Grid>
                <Box mt={8} display="flex" justifyContent="center" gap={2} >
                    <Button variant="contained" onClick={fetchPointDetails}>
                        Point Details
                    </Button>
                    <Button variant="contained" onClick={fetchTopupPoints}>
                        Topup Point Details
                    </Button>
                    <Button variant="outlined" color="inherit" onClick={() => navigate(-1)}>
                        Return Back
                    </Button>
                </Box>
                <Divider sx={{ my: 4 }} />

                {
                    permissions?.some((pre) => pre.name === "allow_create" && pre.status === 1) && (
                        <>
                            <Box mb={2}>
                                <Button
                                    variant="outlined"
                                    onClick={() => setReceiptFormOpen(!receiptFormOpen)}
                                    fullWidth
                                >
                                    {receiptFormOpen ? 'Hide Receipt Form' : 'Apply Loyalty Point By Receipt'}
                                </Button>
                            </Box>
                            <Collapse in={receiptFormOpen}>
                                <Box mb={4} p={2} sx={{ border: '1px solid #eee', borderRadius: 2 }}>
                                    <Typography variant="h6" fontWeight="bold" align="center" mb={2}>
                                        Apply Loyalty Point By Receipt
                                    </Typography>
                                    <form onSubmit={handleApplyPoint}>
                                        <Grid container spacing={2}>
                                            <Grid item xs={12}>
                                                <Typography fontWeight="500" mb={0.5}>Receipt Number</Typography>
                                                <input
                                                    style={{
                                                        width: '100%',
                                                        padding: '10px',
                                                        border: `1px solid ${receiptErrors.receiptNumber ? 'red' : '#ccc'}`,
                                                        borderRadius: '4px',
                                                    }}
                                                    type="text"
                                                    name="receiptNumber"
                                                    placeholder="Enter receipt number"
                                                    value={receiptData.receiptNumber}
                                                    onChange={handleChange}
                                                />
                                                {receiptErrors.receiptNumber && (
                                                    <Typography color="error" variant="caption">
                                                        {receiptErrors.receiptNumber}
                                                    </Typography>
                                                )}
                                            </Grid>
                                            <Grid item xs={12}>
                                                <Typography fontWeight="500" mb={0.5}>Receipt Amount</Typography>
                                                <input
                                                    style={{
                                                        width: '100%',
                                                        padding: '10px',
                                                        border: `1px solid ${receiptErrors.receiptAmount ? 'red' : '#ccc'}`,
                                                        borderRadius: '4px',
                                                    }}
                                                    type="text"
                                                    name="receiptAmount"
                                                    placeholder="Enter receipt amount"
                                                    value={receiptData.receiptAmount}
                                                    onChange={handleChange}
                                                />
                                                {receiptErrors.receiptAmount && (
                                                    <Typography color="error" variant="caption">
                                                        {receiptErrors.receiptAmount}
                                                    </Typography>
                                                )}
                                            </Grid>
                                            <Grid item xs={12}>
                                                <Button
                                                    disabled={receiptLoading || diableAll}
                                                    type="submit"
                                                    fullWidth
                                                    variant="contained"
                                                    sx={{
                                                        textTransform: 'none',
                                                        fontWeight: 'bold',
                                                    }}
                                                >
                                                    {receiptLoading ? 'Applying...' : 'Apply Point'}
                                                </Button>
                                            </Grid>
                                        </Grid>
                                    </form>
                                </Box>
                            </Collapse>
                            <Box mb={2}>
                                <Button
                                    variant="outlined"
                                    onClick={() => setTopupFormOpen(!topupFormOpen)}
                                    fullWidth
                                >
                                    {topupFormOpen ? 'Hide TopUp Form' : 'TopUp Loyalty Points'}
                                </Button>
                            </Box>
                            <Collapse in={topupFormOpen}>
                                <Box mb={4} p={2} sx={{ border: '1px solid #eee', borderRadius: 2 }}>
                                    <Typography variant="h6" fontWeight="bold" align="center" mb={2}>
                                        TopUp Loyalty Points
                                    </Typography>
                                    <form onSubmit={handleTopupPoint}>
                                        <Grid container spacing={2}>
                                            <Grid item xs={12}>
                                                <Typography fontWeight="500" mb={0.5}>Points</Typography>
                                                <input
                                                    style={{
                                                        width: '100%',
                                                        padding: '10px',
                                                        border: `1px solid ${topupErrors.topupAmount ? 'red' : '#ccc'}`,
                                                        borderRadius: '4px',
                                                    }}
                                                    type="number"
                                                    min="0"
                                                    name="topupAmount"
                                                    placeholder="Enter Points"
                                                    value={topupAmount}
                                                    onChange={handleTopupChange}
                                                />
                                                {topupErrors.topupAmount && (
                                                    <Typography color="error" variant="caption">
                                                        {topupErrors.topupAmount}
                                                    </Typography>
                                                )}
                                            </Grid>
                                            {/* <Grid item xs={12}>
                                                <Typography fontWeight="500" mb={0.5}>Remarks</Typography>
                                                <input
                                                    style={{
                                                        width: '100%',
                                                        padding: '10px',
                                                        border: '1px solid #ccc',
                                                        borderRadius: '4px',
                                                    }}
                                                    type="text"
                                                    name="topupRemarks"
                                                    placeholder="Enter remarks" 
                                                    value={topupRemarks}
                                                    onChange={handleRemarksChange}
                                                />
                                            </Grid> */}
                                            <Grid item xs={12}>
                                                <Button
                                                    disabled={topupLoading || diableAll}
                                                    type="submit"
                                                    fullWidth
                                                    variant="contained"
                                                    sx={{
                                                        textTransform: 'none',
                                                        fontWeight: 'bold',
                                                    }}
                                                >
                                                    {topupLoading ? 'Processing...' : 'TopUp Points'}
                                                </Button>
                                            </Grid>
                                        </Grid>
                                    </form>
                                </Box>
                            </Collapse>
                        </>
                    )
                }
            </Paper>
            <Snackbar autoHideDuration={3000} anchorOrigin={{ vertical: "top", horizontal: "right" }} open={open} onClose={() => setOpen(false)}>
                <Alert onClose={() => setOpen(false)} severity={messageState} sx={{ width: '100%' }}>{message}</Alert>
            </Snackbar>
            <Modal
                open={pointsModalOpen}
                onClose={() => setPointsModalOpen(false)}
                aria-labelledby="adjust-points-modal"
            >
                <Box sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: 400,
                    bgcolor: 'background.paper',
                    boxShadow: 24,
                    p: 4,
                    borderRadius: 2,
                }}>
                    <Typography id="adjust-points-modal" variant="h6" component="h2" fontWeight="bold" mb={-2}>
                        Deduct Loyalty Points
                    </Typography>
                    <p style={{ marginBottom: '18px' }}>(Only manually added points can be deducted)</p>
                    {parseInt(customerData?.manual_additon || 0) === 0 && (
                        <Typography color="error" variant="body2" textAlign="center" mb={2}>
                            No manually added points available for deduction.
                        </Typography>
                    )}
                    <Box display="flex" alignItems="center" justifyContent="center" mb={3}>
                        <IconButton
                            color="primary"
                            onClick={() => setPointsAdjustment(prev => Math.max(Math.abs(prev) - 1, 0))}
                            disabled={adjustmentLoading}
                        >
                            <RemoveIcon />
                        </IconButton>
                        <input
                            type="number"
                            min="0"
                            max={customerData?.manual_additon-customerData?.manual_deduction || 0}
                            value={Math.abs(pointsAdjustment)}
                            onChange={(e) => {
                                const value = e.target.value === '' ? 0 : parseInt(e.target.value, 10);
                                const maxDeduction = parseInt(customerData?.manual_additon-customerData?.manual_deduction || 0);
                                const validValue = Math.min(value >= 0 ? value : 0, maxDeduction);
                                setPointsAdjustment(validValue);
                            }}
                            disabled={adjustmentLoading}
                            style={{
                                width: '80px',
                                padding: '8px',
                                margin: '0 16px',
                                textAlign: 'center',
                                fontSize: '1.2rem',
                                border: '1px solid #ccc',
                                borderRadius: '4px',
                            }}
                        />
                        <IconButton
                            color="primary"
                            onClick={() => {
                                const maxDeduction = parseInt(customerData?.manual_additon-customerData?.manual_deduction || 0);
                                setPointsAdjustment(prev => {
                                    const newValue = Math.abs(prev) + 1;
                                    return newValue <= maxDeduction ? newValue : maxDeduction;
                                });
                            }}
                            disabled={adjustmentLoading || pointsAdjustment >= parseInt(customerData?.manual_additon || 0)}
                        >
                            <AddIcon />
                        </IconButton>
                    </Box>
                    <Typography variant="body2" color="text.secondary" mb={3} textAlign="center">
                        Current Points: {customerData?.customer?.current_points || '0'}
                        <br />
                        Available for Deduction: {customerData?.manual_additon-customerData?.manual_deduction || '0'}
                        <br />
                        New Points: {(parseInt(customerData?.customer?.current_points || 0) - pointsAdjustment) || '0'}
                    </Typography>
                    <Box display="flex" justifyContent="space-between">
                        <Button
                            variant="outlined"
                            onClick={() => {
                                setPointsModalOpen(false);
                                setPointsAdjustment(0);
                            }}
                            disabled={adjustmentLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="contained"
                            onClick={handleAdjustPoints}
                            disabled={
                                adjustmentLoading ||
                                pointsAdjustment === 0 ||
                                parseInt(customerData?.manual_additon || 0) === 0
                            }
                        >
                            {adjustmentLoading ? 'Saving...' : 'Deduct Points'}
                        </Button>
                    </Box>
                </Box>
            </Modal>
        </Box>
    );
}