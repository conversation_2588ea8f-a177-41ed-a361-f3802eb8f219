import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';

const FilteredBox = styled(Box)(({ theme }) => ({
  background: "#f9f9f9",
  padding: "5px 10px",
  borderRadius: "5px",
  "& p": {
    margin: "3px 0",
    marginRight: "10px",
    display: "inline-block",
    background: "#dedede",
    borderRadius: "10px",
    padding: "2px 5px",
  },
  "& svg": {
    fontSize: "15px",
    cursor: "pointer",
    position: "relative",
    top: "3px",
    background: theme.palette.primary.dark,
    color: "#fff",
    borderRadius: "50%",
    padding: "2px",
    marginLeft: "2px",
  },
}));

const ActiveFilters = ({ filters, onRemoveFilter, showTime = true }) => {
  // Format date to display with optional time
  const formatDateTime = (date) => {
    if (!date) return "";
    
    const dateObj = new Date(date);
    
    // Check if the date is valid
    if (isNaN(dateObj.getTime())) return "";
    
    if (showTime) {
      // Format with time
      return dateObj.toLocaleString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      // Format without time
      return dateObj.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  // Format date range for display
  const formatDateRange = (start, end) => {
    const startDisplay = formatDateTime(start);
    const endDisplay = formatDateTime(end);
    
    if (startDisplay && endDisplay) {
      return `${startDisplay} to ${endDisplay}`;
    } else if (startDisplay) {
      return `From ${startDisplay}`;
    } else if (endDisplay) {
      return `To ${endDisplay}`;
    }
    
    return "";
  };

  return (
    <FilteredBox m={2} p={2}>
      <span>Active Filters: </span>
      
      {/* Sites filter */}
      {filters.sites && filters.sites.length > 0 && (
        <p>
          <span>Sites: {filters.sites.map(site => site.manualStoreCode || site.name || site).join(", ")}</span>
          <CloseIcon
            fontSize="small"
            onClick={() => onRemoveFilter("sites")}
          />
        </p>
      )}
      
      {/* States filter */}
      {filters.states && filters.states.length > 0 && (
        <p>
          <span>States: {filters.states.map(state => state.state || state.name || state).join(", ")}</span>
          <CloseIcon
            fontSize="small"
            onClick={() => onRemoveFilter("states")}
          />
        </p>
      )}
      
      {/* Financial Year filter */}
      {filters.financialYear && (
        <p>
          <span>Financial Year: {filters.financialYear.name || filters.financialYear}</span>
          <CloseIcon
            fontSize="small"
            onClick={() => onRemoveFilter("financialYear")}
          />
        </p>
      )}
      
      {/* Date Range filter */}
      {(filters.startDateTime || filters.endDateTime) && (
        <p>
          <span>
            Date Range: {formatDateRange(filters.startDateTime, filters.endDateTime)}
          </span>
          <CloseIcon
            fontSize="small"
            onClick={() => onRemoveFilter("dateRange")}
          />
        </p>
      )}
    </FilteredBox>
  );
};

export default ActiveFilters;